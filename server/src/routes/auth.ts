import { Hono } from "hono";
import { PrismaClient } from "@prisma/client";
import { auth } from "../lib/auth";
import { generateId } from "better-auth";

const prisma = new PrismaClient();
const authApp = new Hono();

/**
 * POST /api/auth/register-visitor
 * Custom registration for visitors with additional fields
 */
authApp.post("/register-visitor", async (c) => {
  try {
    const body = await c.req.json();
    const {
      email,
      password,
      name,
      nomorTelepon,
      tipePengunjung,
      organizationId
    } = body;

    // Validate required fields
    if (!email || !password || !name || !organizationId) {
      return c.json({
        success: false,
        error: "Missing required fields"
      }, 400);
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return c.json({
        success: false,
        error: "User already exists"
      }, 400);
    }

    // Verify organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      return c.json({
        success: false,
        error: "Invalid organization"
      }, 400);
    }

    // Create user with better-auth
    const signUpResult = await auth.api.signUpEmail({
      body: {
        email,
        password,
        name
      },
      headers: c.req.raw.headers
    });

    if (signUpResult.error) {
      return c.json({
        success: false,
        error: signUpResult.error.message || "Failed to create user"
      }, 400);
    }

    // Update user with additional fields and create organization membership
    const updatedUser = await prisma.$transaction(async (tx) => {
      // Update user with additional fields
      const user = await tx.user.update({
        where: { email },
        data: {
          nomorTelepon,
          tipePengunjung: tipePengunjung === 'karyawan' ? 'KARYAWAN' : 'UMUM',
          organizationId
        }
      });

      // Create organization membership
      await tx.member.create({
        data: {
          id: generateId(),
          userId: user.id,
          organizationId: organizationId,
          role: 'member', // Default role for registered users
          createdAt: new Date()
        }
      });

      return user;
    });

    return c.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name
      }
    });

  } catch (error) {
    console.error("Registration error:", error);
    return c.json({
      success: false,
      error: "Registration failed"
    }, 500);
  }
});

/**
 * GET /api/auth/user-status
 * Get current user's approval status
 */
authApp.get("/user-status", async (c) => {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    if (!session?.user) {
      return c.json({
        success: false,
        error: "Not authenticated"
      }, 401);
    }

    // Get user details including approval status
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        approvalStatus: true
      }
    });

    if (!user) {
      return c.json({
        success: false,
        error: "User not found"
      }, 404);
    }

    return c.json({
      success: true,
      approvalStatus: user.approvalStatus,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error("Error getting user status:", error);
    return c.json({
      success: false,
      error: "Failed to get user status"
    }, 500);
  }
});

// Mount Better Auth handlers - catch-all untuk semua routes kecuali custom routes
authApp.all("*", async (c) => {
  // Skip custom routes
  const path = c.req.path;
  if (path.includes('/register-visitor') || path.includes('/user-status')) {
    // Let it fall through to our custom handlers above
    return c.notFound();
  }

  try {
    const response = await auth.handler(c.req.raw);
    return new Response(response.body, {
      status: response.status,
      headers: response.headers,
    });
  } catch (error) {
    console.error('Better Auth handler error:', error);
    return c.json({ error: 'Authentication error' }, 500);
  }
});

export default authApp;