import { <PERSON>o } from "hono";
import type { ApiResponse, DashboardStats, ChartDataPoint, VisitorData } from "../types/shared";
import { requireAuth, requireUser, requireAdmin, requireSuperAdmin } from "../middleware/auth";
import { DashboardService } from "../services/dashboardService";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const dashboardApp = new Hono();

dashboardApp.get("/stats", requireAuth, requireUser, async (c) => {
	const stats: DashboardStats = {
		totalPengunjungTerdaftar: 17,
		totalPengunjungDisetujui: 10,
		totalPengunjungDitolak: 2,
		totalPengunjungKadaluwarsa: 5,
	};

	return c.json(stats, { status: 200 });
})

.get("/chart", requireAuth, requireUser, async (c) => {
	const chartData: ChartDataPoint[] = [
		{ day: "Senin", value: 40 },
		{ day: "Se<PERSON><PERSON>", value: 35 },
		{ day: "Rabu", value: 45 },
		{ day: "Kamis", value: 95 },
		{ day: "Jumat", value: 80 },
		{ day: "Sabtu", value: 65 },
	];

	return c.json(chartData, { status: 200 });
})

.get("/chart/weekly", requireAuth, requireUser, async (c) => {
	const weeklyData: ChartDataPoint[] = [
		{ day: "Minggu 1", value: 280 },
		{ day: "Minggu 2", value: 320 },
		{ day: "Minggu 3", value: 290 },
		{ day: "Minggu 4", value: 410 },
	];

	return c.json(weeklyData, { status: 200 });
})

.get("/visitors", requireAuth, requireUser, async (c) => {
	const visitors: VisitorData[] = [
		{
			id: "1",
			name: "Rina Pratama",
			company: "PT. Teknologi Maju",
			type: "PAM",
			status: "approved",
			date: "2024-01-15",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "2",
			name: "Budi Santoso",
			company: "CV. Berkah Jaya",
			type: "PAM",
			status: "pending",
			date: "2024-01-16",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "3",
			name: "Sari Wijaya",
			company: "PT. Industri Besar",
			type: "PAM",
			status: "approved",
			date: "2024-01-17",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "4",
			name: "Ahmad Fauzi",
			company: "PT. Maju Bersama",
			type: "PAM",
			status: "approved",
			date: "2024-01-18",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "5",
			name: "Dewi Sartika",
			company: "CV. Sukses Mandiri",
			type: "SMA",
			status: "pending",
			date: "2024-01-19"
		},
		{
			id: "6",
			name: "Rudi Hermawan",
			company: "PT. Global Tech",
			type: "SMA",
			status: "approved",
			date: "2024-01-20",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "7",
			name: "Maya Sari",
			company: "PT. Digital Solutions",
			type: "IBM",
			status: "rejected",
			date: "2024-01-21"
		},
		{
			id: "8",
			name: "Andi Wijaya",
			company: "CV. Inovasi Teknologi",
			type: "IBM",
			status: "approved",
			date: "2024-01-22",
			location: { lat: -6.2088, lng: 106.8456 }
		}
	];

	return c.json(visitors, { status: 200 });
});

// Legacy dashboard routes (for backward compatibility)
dashboardApp.get("/chart", requireAuth, requireUser, async (c) => {
	try {
		const chartData = await DashboardService.getChartData('daily');
		if (chartData.datasets && chartData.datasets[0] && chartData.labels) {
			return c.json(chartData.datasets[0].data.map((value, index) => ({
				day: chartData.labels[index],
				value
			})), { status: 200 });
		}
		return c.json({ error: "Invalid chart data structure" }, 500);
	} catch (error) {
		console.error('Error fetching chart data:', error);
		return c.json({ error: "Failed to fetch chart data" }, 500);
	}
});

dashboardApp.get("/chart/weekly", requireAuth, requireUser, async (c) => {
	try {
		const chartData = await DashboardService.getChartData('weekly');
		if (chartData.datasets && chartData.datasets[0] && chartData.labels) {
			return c.json(chartData.datasets[0].data.map((value, index) => ({
				day: chartData.labels[index],
				value
			})), { status: 200 });
		}
		return c.json({ error: "Invalid chart data structure" }, 500);
	} catch (error) {
		console.error('Error fetching weekly chart data:', error);
		return c.json({ error: "Failed to fetch weekly chart data" }, 500);
	}
});

// New endpoint: Get all visitors (for superadmin)
dashboardApp.get("/all-visitors", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const visitors = await prisma.user.findMany({
			where: {
				role: 'user', // Only get visitors (users with role 'user')
				organizationId: { not: null } // Must have an organization
			},
			include: {
				organization: {
					select: {
						id: true,
						name: true,
						slug: true
					}
				}
			},
			orderBy: {
				createdAt: 'desc'
			}
		});

		// Transform data to match frontend expectations
		const transformedVisitors = visitors.map(visitor => ({
			id: visitor.id,
			name: visitor.name,
			email: visitor.email,
			nomorTelepon: visitor.nomorTelepon,
			tipePengunjung: visitor.tipePengunjung,
			organization: visitor.organization,
			organizationId: visitor.organizationId,
			createdAt: visitor.createdAt,
			status: visitor.approvalStatus?.toLowerCase() || 'pending'
		}));

		return c.json({
			success: true,
			data: transformedVisitors
		});
	} catch (error) {
		console.error("Error fetching all visitors:", error);
		return c.json({
			success: false,
			error: "Failed to fetch visitors"
		}, 500);
	}
});

// New endpoint: Get visitors by organization (for admin)
dashboardApp.get("/visitors-by-org/:orgId", requireAuth, requireAdmin, async (c) => {
	try {
		const orgId = c.req.param("orgId");
		const user: any = (c as any).get("user");

		// For non-superadmin users, ensure they can only access their own organization's visitors
		if (user.role !== 'superadmin') {
			// Get user's organization membership
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: orgId
				}
			});

			if (!userMembership) {
				return c.json({
					success: false,
					error: "Access denied: You can only view visitors from your organization"
				}, 403);
			}
		}

		const visitors = await prisma.user.findMany({
			where: {
				role: 'user',
				organizationId: orgId
			},
			include: {
				organization: {
					select: {
						id: true,
						name: true,
						slug: true
					}
				}
			},
			orderBy: {
				createdAt: 'desc'
			}
		});

		// Transform data to match frontend expectations
		const transformedVisitors = visitors.map(visitor => ({
			id: visitor.id,
			name: visitor.name,
			email: visitor.email,
			nomorTelepon: visitor.nomorTelepon,
			tipePengunjung: visitor.tipePengunjung,
			organization: visitor.organization,
			organizationId: visitor.organizationId,
			createdAt: visitor.createdAt,
			status: visitor.approvalStatus?.toLowerCase() || 'pending'
		}));

		return c.json({
			success: true,
			data: transformedVisitors
		});
	} catch (error) {
		console.error("Error fetching visitors by organization:", error);
		return c.json({
			success: false,
			error: "Failed to fetch visitors"
		}, 500);
	}
});

// Enhanced stats endpoint with real data
dashboardApp.get("/real-stats", requireAuth, requireUser, async (c) => {
	try {
		const user: any = (c as any).get("user");
		let whereClause: any = {
			role: 'user',
			organizationId: { not: null }
		};

		// For non-superadmin users, filter by their organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: { userId: user.id },
				select: { organizationId: true }
			});

			if (userMembership) {
				whereClause.organizationId = userMembership.organizationId;
			}
		}

		const totalVisitors = await prisma.user.count({
			where: whereClause
		});

		// For now, we'll use simple calculations
		// These can be enhanced with actual status tracking later
		const stats: DashboardStats = {
			totalPengunjungTerdaftar: totalVisitors,
			totalPengunjungDisetujui: Math.floor(totalVisitors * 0.7), // 70% approved
			totalPengunjungDitolak: Math.floor(totalVisitors * 0.1), // 10% rejected
			totalPengunjungKadaluwarsa: Math.floor(totalVisitors * 0.2), // 20% expired
		};

		return c.json(stats, { status: 200 });
	} catch (error) {
		console.error("Error fetching real stats:", error);
		return c.json({
			totalPengunjungTerdaftar: 0,
			totalPengunjungDisetujui: 0,
			totalPengunjungDitolak: 0,
			totalPengunjungKadaluwarsa: 0,
		}, { status: 200 });
	}
});

// Visitor approval endpoints
dashboardApp.post("/approve-visitor/:visitorId", requireAuth, requireAdmin, async (c) => {
	try {
		const visitorId = c.req.param("visitorId");
		const user: any = (c as any).get("user");

		// Get visitor details
		const visitor = await prisma.user.findUnique({
			where: { id: visitorId },
			include: { organization: true }
		});

		if (!visitor) {
			return c.json({
				success: false,
				error: "Visitor not found"
			}, 404);
		}

		// For non-superadmin users, ensure they can only approve visitors from their organization
		if (user.role !== 'superadmin') {
			if (!visitor.organizationId) {
				return c.json({
					success: false,
					error: "Visitor has no organization assigned"
				}, 400);
			}

			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: visitor.organizationId
				}
			});

			if (!userMembership) {
				return c.json({
					success: false,
					error: "Access denied: You can only approve visitors from your organization"
				}, 403);
			}
		}

		// Update visitor approval status
		const updatedVisitor = await prisma.user.update({
			where: { id: visitorId },
			data: { approvalStatus: 'APPROVED' },
			include: { organization: true }
		});

		return c.json({
			success: true,
			data: {
				id: updatedVisitor.id,
				name: updatedVisitor.name,
				email: updatedVisitor.email,
				approvalStatus: updatedVisitor.approvalStatus,
				organization: updatedVisitor.organization
			}
		});
	} catch (error) {
		console.error("Error approving visitor:", error);
		return c.json({
			success: false,
			error: "Failed to approve visitor"
		}, 500);
	}
});

dashboardApp.post("/reject-visitor/:visitorId", requireAuth, requireAdmin, async (c) => {
	try {
		const visitorId = c.req.param("visitorId");
		const user: any = (c as any).get("user");

		// Get visitor details
		const visitor = await prisma.user.findUnique({
			where: { id: visitorId },
			include: { organization: true }
		});

		if (!visitor) {
			return c.json({
				success: false,
				error: "Visitor not found"
			}, 404);
		}

		// For non-superadmin users, ensure they can only reject visitors from their organization
		if (user.role !== 'superadmin') {
			if (!visitor.organizationId) {
				return c.json({
					success: false,
					error: "Visitor has no organization assigned"
				}, 400);
			}

			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: visitor.organizationId
				}
			});

			if (!userMembership) {
				return c.json({
					success: false,
					error: "Access denied: You can only reject visitors from your organization"
				}, 403);
			}
		}

		// Delete visitor from database (as requested)
		await prisma.$transaction(async (tx) => {
			// Delete member record first
			await tx.member.deleteMany({
				where: { userId: visitorId }
			});

			// Delete user record
			await tx.user.delete({
				where: { id: visitorId }
			});
		});

		return c.json({
			success: true,
			message: "Visitor rejected and removed from database"
		});
	} catch (error) {
		console.error("Error rejecting visitor:", error);
		return c.json({
			success: false,
			error: "Failed to reject visitor"
		}, 500);
	}
});

export default dashboardApp;