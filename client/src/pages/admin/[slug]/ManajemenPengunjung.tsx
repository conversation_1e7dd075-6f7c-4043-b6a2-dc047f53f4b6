import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../../components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '../../../components/ui/avatar';
import { Filter, Search } from 'lucide-react';

// Types
type DashboardStats = {
  totalPengunjungTerdaftar: number;
  totalPengunjungDisetujui: number;
  totalPengunjungDitolak: number;
  totalPengunjungKadaluwarsa: number;
};

interface Organization {
  id: string;
  name: string;
  slug: string;
}

type VisitorData = {
  id: string;
  name: string;
  email: string;
  nomorTelepon?: string;
  tipePengunjung: 'UMUM' | 'KARYAWAN' | null;
  organization: Organization;
  organizationId: string;
  createdAt: string;
  status: 'approved' | 'pending' | 'rejected' | 'expired';
};

// Using relative paths for single origin deployment

export function ManajemenPengunjung() {
  const { slug } = useParams<{ slug: string }>();
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // First, get the organization by slug
        if (slug) {
          try {
            const orgRes = await fetch(`/api/organization/${slug}`, {
              credentials: 'include',
            });
            if (orgRes.ok) {
              const orgData = await orgRes.json();
              if (orgData.success) {
                setOrganization(orgData.organization);

                // Fetch visitors for this organization
                const visitorsRes = await fetch(`/api/dashboard/visitors-by-org/${orgData.organization.id}`, {
                  credentials: 'include',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                });
                if (visitorsRes.ok) {
                  const visitorsData = await visitorsRes.json();
                  if (visitorsData.success) {
                    setVisitors(visitorsData.data);
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error fetching organization or visitors:', error);
          }
        }

        // Fetch real stats
        try {
          const statsRes = await fetch(`/api/dashboard/real-stats`, {
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (statsRes.ok) {
            const statsData = await statsRes.json();
            setStats(statsData);
          }
        } catch (error) {
          console.error('Error fetching stats:', error);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Manajemen Pengunjung {organization ? `- ${organization.name}` : ''}
        </h1>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-3 lg:gap-4">
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="p-3 lg:p-4">
            <div className="text-xs lg:text-sm text-gray-600 mb-1 leading-tight">
              Total Pengunjung Terdaftar
            </div>
            <div className="text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900">
              {stats.totalPengunjungTerdaftar}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="p-3 lg:p-4">
            <div className="text-xs lg:text-sm text-gray-600 mb-1 leading-tight">
              Total Pengunjung Disetujui
            </div>
            <div className="text-xl lg:text-2xl xl:text-3xl font-bold text-green-600">
              {stats.totalPengunjungDisetujui}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="p-3 lg:p-4">
            <div className="text-xs lg:text-sm text-gray-600 mb-1 leading-tight">
              Total Pengunjung Ditolak
            </div>
            <div className="text-xl lg:text-2xl xl:text-3xl font-bold text-red-600">
              {stats.totalPengunjungDitolak}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="p-3 lg:p-4">
            <div className="text-xs lg:text-sm text-gray-600 mb-1 leading-tight">
              Total Pengunjung Kadaluwarsa
            </div>
            <div className="text-xl lg:text-2xl xl:text-3xl font-bold text-orange-600">
              {stats.totalPengunjungKadaluwarsa}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Visitor Management Section */}
      <VisitorGrid visitors={visitors} />
    </div>
  );
}

function VisitorGrid({ visitors }: { visitors: VisitorData[] }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || visitor.tipePengunjung === selectedType;
    return matchesSearch && matchesType;
  });

  return (
    <div className="space-y-4 max-w-full">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-3 lg:gap-4">
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-full sm:w-48 h-10">
            <Filter className="h-4 w-4 mr-2 flex-shrink-0" />
            <SelectValue placeholder="Tipe Pengunjung" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua</SelectItem>
            <SelectItem value="KARYAWAN">Karyawan</SelectItem>
            <SelectItem value="UMUM">Umum</SelectItem>
          </SelectContent>
        </Select>

        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 flex-shrink-0" />
          <input
            type="text"
            placeholder="Search by name"
            className="w-full h-10 pl-10 pr-4 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Visitor Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 lg:gap-4">
        {filteredVisitors.length > 0 ? (
          filteredVisitors.map((visitor) => (
            <VisitorCard key={visitor.id} visitor={visitor} />
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500">
            <p>Tidak ada pengunjung yang ditemukan</p>
          </div>
        )}
      </div>
    </div>
  );
}

function VisitorCard({ visitor }: { visitor: VisitorData }) {
  return (
    <Card className="bg-white border border-gray-200 hover:shadow-md transition-all duration-200 h-full">
      <CardContent className="p-3 lg:p-4 h-full">
        <div className="flex flex-col items-center text-center space-y-2 lg:space-y-3 h-full">
          <Avatar className="w-12 h-12 lg:w-16 lg:h-16 flex-shrink-0">
            <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${visitor.name}`} />
            <AvatarFallback className="bg-gray-200 text-gray-600 text-sm lg:text-base">
              {visitor.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>

          <div className="space-y-1 flex-1 min-w-0">
            <h3 className="font-medium text-gray-900 text-sm lg:text-base truncate px-1">
              Nama: {visitor.name}
            </h3>
            <p className="text-xs lg:text-sm text-gray-600 truncate px-1">
              Tipe Pengunjung: <span className="font-medium">{visitor.tipePengunjung || 'Tidak Diketahui'}</span>
            </p>
            <p className="text-xs text-gray-500 truncate px-1">
              Email: {visitor.email}
            </p>
            {visitor.nomorTelepon && (
              <p className="text-xs text-gray-500 truncate px-1">
                Telepon: {visitor.nomorTelepon}
              </p>
            )}
          </div>

          <Button
            size="sm"
            className="w-full bg-orange-500 hover:bg-orange-600 text-white text-xs lg:text-sm py-2 flex-shrink-0"
          >
            Kirim Pengingatkan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
