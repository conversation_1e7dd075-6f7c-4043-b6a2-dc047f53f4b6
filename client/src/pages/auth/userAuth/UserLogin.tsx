import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { PAMLogo } from '../../../components/ui/PAMLogo';
import { signIn, getSession } from '../../../lib/auth-client';

export function UserLogin() {
  const [formData, setFormData] = useState({
    usernameOrEmail: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  // Check for success message from registration
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      // Clear the state to prevent showing message on refresh
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, navigate, location.pathname]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await signIn.email({
        email: formData.usernameOrEmail,
        password: formData.password,
      });

      if (result.error) {
        setError(result.error.message || 'Login failed');
        return;
      }

      // Get session to access complete user data including role
      const session = await getSession();
      console.log('Login result:', result);
      console.log('Session data:', session);
      console.log('User role detected:', (session?.data as any)?.user?.role);

      const userRole = (session?.data as any)?.user?.role;

      // Check if user has 'user' role
      if (userRole !== 'user') {
        setError('Access denied. This login is for users only.');
        return;
      }

      // Check approval status for visitors
      try {
        const statusResponse = await fetch('/api/auth/user-status', {
          credentials: 'include'
        });

        if (statusResponse.ok) {
          const statusResult = await statusResponse.json();

          if (statusResult.success) {
            const approvalStatus = statusResult.approvalStatus;

            if (approvalStatus === 'PENDING') {
              setError('Your account is pending approval. Please wait for administrator approval.');
              return;
            }

            if (approvalStatus === 'REJECTED') {
              setError('Your account has been rejected. Please contact administrator.');
              return;
            }

            // Only allow login if approved or if approvalStatus is null (for existing users)
            if (approvalStatus === 'APPROVED' || approvalStatus === null) {
              // Redirect to user dashboard
              navigate('/user/quiz', { replace: true });
              return;
            }
          }
        }

        // If we can't check status, show error
        setError('Unable to verify account status. Please try again.');

      } catch (statusError) {
        console.error('Error checking approval status:', statusError);
        setError('Unable to verify account status. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    navigate('/user/forgot-password');
  };

  const handleRegister = () => {
    navigate('/user/register');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Fixed mobile layout */}
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header with Logo */}
        <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
          <PAMLogo width={108} height={108} />
        </div>

        {/* Form Content */}
        <div className="flex-1 px-6 pb-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 text-left">LOGIN</h1>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg mb-6 text-sm">
              {successMessage}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-6 text-sm">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            {/* Username/Email Field */}
            <div>
              <Input
                type="text"
                value={formData.usernameOrEmail}
                onChange={(e) => handleInputChange('usernameOrEmail', e.target.value)}
                required
                className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                placeholder="Username/Email"
              />
            </div>

            {/* Password Field */}
            <div>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
                className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                placeholder="Password"
              />
            </div>

            {/* Forgot Password Link */}
            <div className="text-right pt-2">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="text-orange-600 hover:text-orange-500 text-base font-medium"
              >
                Lupa Password?
              </button>
            </div>

            {/* Login Button */}
            <div className="pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                {isLoading ? 'Login...' : 'Login'}
              </Button>
            </div>
          </form>

          {/* Register Link */}
          <div className="text-center mt-8">
            <p className="text-base text-gray-600">
              Belum Punya Akun?{' '}
              <button
                onClick={handleRegister}
                className="text-orange-600 hover:text-orange-500 font-medium"
              >
                Register
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
