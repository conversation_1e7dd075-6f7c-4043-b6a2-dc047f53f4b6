import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../components/ui/tabs';
import { StatsCards } from '../../components/dashboard/StatsCards';
import { VisitorChart } from '../../components/dashboard/VisitorChart';
import { HeatmapCard } from '../../components/dashboard/HeatmapCard';
import { EnhancedRecentVisitors } from '../../components/dashboard/EnhancedRecentVisitors';

// Types
type DashboardStats = {
  totalPengunjungTerdaftar: number;
  totalPengunjungDisetujui: number;
  totalPengunjungDitolak: number;
  totalPengunjungKadaluwarsa: number;
};

type ChartDataPoint = {
  day: string;
  value: number;
};

interface Organization {
  id: string;
  name: string;
  slug: string;
}

type VisitorData = {
  id: string;
  name: string;
  email: string;
  nomorTelepon?: string;
  tipePengunjung: 'UMUM' | 'KARYAWAN' | null;
  organization: Organization;
  organizationId: string;
  createdAt: string;
  status: 'approved' | 'pending' | 'rejected';
};

// Using relative paths for single origin deployment

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch organizations first
        const orgsResponse = await fetch('/api/organizations', {
          credentials: 'include'
        });
        if (orgsResponse.ok) {
          const orgsResult = await orgsResponse.json();
          if (orgsResult.success) {
            setOrganizations(orgsResult.data);
            if (orgsResult.data.length > 0) {
              setActiveTab(orgsResult.data[0].id);
            }
          }
        }

        // Fetch all visitors for superadmin
        const visitorsResponse = await fetch('/api/dashboard/all-visitors', {
          credentials: 'include'
        });
        if (visitorsResponse.ok) {
          const visitorsResult = await visitorsResponse.json();
          if (visitorsResult.success) {
            const fetchedVisitors = visitorsResult.data;
            setVisitors(fetchedVisitors);

            // Calculate stats from visitors data
            const totalTerdaftar = fetchedVisitors.length;
            const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
            const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
            const totalKadaluwarsa = 5; // Dummy data for now

            setStats({
              totalPengunjungTerdaftar: totalTerdaftar,
              totalPengunjungDisetujui: totalDisetujui,
              totalPengunjungDitolak: totalDitolak,
              totalPengunjungKadaluwarsa: totalKadaluwarsa,
            });

            // Generate chart data based on visitor registration dates (last 7 days)
            const last7Days = Array.from({ length: 7 }, (_, i) => {
              const date = new Date();
              date.setDate(date.getDate() - (6 - i));
              return date;
            });

            const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];

            const chartData = last7Days.map(date => {
              const dayName = dayNames[date.getDay()];
              const visitorsOnDay = fetchedVisitors.filter((visitor: VisitorData) => {
                const visitorDate = new Date(visitor.createdAt);
                return visitorDate.toDateString() === date.toDateString();
              }).length;

              return {
                day: dayName,
                value: visitorsOnDay
              };
            });

            setChartData(chartData);
          } else {
            // Fallback stats if no visitors
            setStats({
              totalPengunjungTerdaftar: 0,
              totalPengunjungDisetujui: 0,
              totalPengunjungDitolak: 0,
              totalPengunjungKadaluwarsa: 0,
            });

            setChartData([
              { day: 'Minggu', value: 0 },
              { day: 'Senin', value: 0 },
              { day: 'Selasa', value: 0 },
              { day: 'Rabu', value: 0 },
              { day: 'Kamis', value: 0 },
              { day: 'Jumat', value: 0 },
              { day: 'Sabtu', value: 0 },
            ]);
          }
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set fallback data
        setStats({
          totalPengunjungTerdaftar: 150,
          totalPengunjungDisetujui: 120,
          totalPengunjungDitolak: 15,
          totalPengunjungKadaluwarsa: 15,
        });
        setChartData([
          { day: 'Sen', value: 12 },
          { day: 'Sel', value: 19 },
          { day: 'Rab', value: 15 },
          { day: 'Kam', value: 25 },
          { day: 'Jum', value: 22 },
          { day: 'Sab', value: 18 },
          { day: 'Min', value: 8 },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getVisitorsByOrganization = (orgId: string) => {
    return visitors.filter(visitor => visitor.organizationId === orgId);
  };

  const handleApproveVisitor = async (visitorId: string) => {
    try {
      const response = await fetch(`/api/dashboard/approve-visitor/${visitorId}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Refresh visitors data and recalculate stats
        const visitorsResponse = await fetch('/api/dashboard/all-visitors', {
          credentials: 'include'
        });
        if (visitorsResponse.ok) {
          const visitorsResult = await visitorsResponse.json();
          if (visitorsResult.success) {
            const fetchedVisitors = visitorsResult.data;
            setVisitors(fetchedVisitors);

            // Recalculate stats
            const totalTerdaftar = fetchedVisitors.length;
            const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
            const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
            const totalKadaluwarsa = 5; // Dummy data for now

            setStats({
              totalPengunjungTerdaftar: totalTerdaftar,
              totalPengunjungDisetujui: totalDisetujui,
              totalPengunjungDitolak: totalDitolak,
              totalPengunjungKadaluwarsa: totalKadaluwarsa,
            });
          }
        }
      } else {
        const error = await response.json();
        alert(`Error approving visitor: ${error.error}`);
      }
    } catch (error) {
      console.error('Error approving visitor:', error);
      alert('Failed to approve visitor');
    }
  };

  const handleRejectVisitor = async (visitorId: string) => {
    try {
      const response = await fetch(`/api/dashboard/reject-visitor/${visitorId}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Refresh visitors data and recalculate stats
        const visitorsResponse = await fetch('/api/dashboard/all-visitors', {
          credentials: 'include'
        });
        if (visitorsResponse.ok) {
          const visitorsResult = await visitorsResponse.json();
          if (visitorsResult.success) {
            const fetchedVisitors = visitorsResult.data;
            setVisitors(fetchedVisitors);

            // Recalculate stats
            const totalTerdaftar = fetchedVisitors.length;
            const totalDisetujui = fetchedVisitors.filter((v: VisitorData) => v.status === 'approved').length;
            const totalDitolak = fetchedVisitors.filter((v: VisitorData) => v.status === 'rejected').length;
            const totalKadaluwarsa = 5; // Dummy data for now

            setStats({
              totalPengunjungTerdaftar: totalTerdaftar,
              totalPengunjungDisetujui: totalDisetujui,
              totalPengunjungDitolak: totalDitolak,
              totalPengunjungKadaluwarsa: totalKadaluwarsa,
            });
          }
        }
      } else {
        const error = await response.json();
        alert(`Error rejecting visitor: ${error.error}`);
      }
    } catch (error) {
      console.error('Error rejecting visitor:', error);
      alert('Failed to reject visitor');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (organizations.length === 0) {
    return (
      <div className="space-y-4 lg:space-y-6 max-w-full">
        <div className="flex-shrink-0">
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Dashboard</h1>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">No organizations found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Dashboard</h1>
      </div>

      {/* Dynamic Tabs based on Organizations */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex-shrink-0">
          <TabsList className={`grid w-full max-w-2xl grid-cols-${Math.min(organizations.length, 4)}`}>
            {organizations.map((org) => (
              <TabsTrigger
                key={org.id}
                value={org.id}
                className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
              >
                {org.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {organizations.map((org) => (
          <TabsContent key={org.id} value={org.id} className="space-y-4 lg:space-y-6 mt-4 lg:mt-6">
            {/* Stats Cards */}
            <StatsCards stats={stats} />

            {/* Chart and Heatmap */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              <VisitorChart data={chartData} />
              <HeatmapCard />
            </div>

            {/* Recent Visitors filtered by organization */}
            <EnhancedRecentVisitors
              visitors={getVisitorsByOrganization(org.id)}
              onApprove={handleApproveVisitor}
              onReject={handleRejectVisitor}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
