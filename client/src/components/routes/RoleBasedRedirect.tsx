import { Navigate } from 'react-router-dom';
import { useSession } from '../../lib/auth-client';
import { useState, useEffect } from 'react';

/**
 * Component to handle role-based redirects for authenticated users
 * Redirects users to their appropriate dashboard based on their role
 * Handles pending approval status for visitors
 */
export function RoleBasedRedirect() {
  const { data: session, isPending } = useSession();
  const [userApprovalStatus, setUserApprovalStatus] = useState<string | null>(null);
  const [checkingApproval, setCheckingApproval] = useState(false);

  useEffect(() => {
    const checkApprovalStatus = async () => {
      if (session?.user && session.user.role === 'user') {
        setCheckingApproval(true);
        try {
          const response = await fetch('/api/auth/user-status', {
            credentials: 'include'
          });
          if (response.ok) {
            const result = await response.json();
            setUserApprovalStatus(result.approvalStatus);
          }
        } catch (error) {
          console.error('Error checking approval status:', error);
        } finally {
          setCheckingApproval(false);
        }
      }
    };

    checkApprovalStatus();
  }, [session]);

  // Show loading while checking session or approval status
  if (isPending || checkingApproval) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If user is authenticated, redirect based on role
  if (session?.user) {
    const userRole = session.user.role;

    if (userRole === 'superadmin') {
      return <Navigate to="/superadmin/dashboard" replace />;
    }

    if (userRole === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    }

    if (userRole === 'user') {
      // Check approval status for visitors
      if (userApprovalStatus === 'PENDING') {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
              <div className="mb-6">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                  <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Menunggu Persetujuan
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                Akun Anda sedang menunggu persetujuan dari administrator.
                Silakan tunggu hingga akun Anda disetujui untuk dapat mengakses sistem.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Refresh Status
              </button>
            </div>
          </div>
        );
      }

      if (userApprovalStatus === 'REJECTED') {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
              <div className="mb-6">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Akses Ditolak
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                Maaf, akun Anda telah ditolak oleh administrator.
                Silakan hubungi administrator untuk informasi lebih lanjut.
              </p>
              <button
                onClick={() => {
                  // Sign out the user
                  fetch('/api/auth/sign-out', { method: 'POST', credentials: 'include' })
                    .then(() => window.location.href = '/user/login');
                }}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Kembali ke Login
              </button>
            </div>
          </div>
        );
      }

      // If approved or null (existing users), redirect to quiz
      if (userApprovalStatus === 'APPROVED' || userApprovalStatus === null) {
        return <Navigate to="/user/quiz" replace />;
      }

      // If status is unknown, show loading or redirect to login
      return <Navigate to="/user/login" replace />;
    }

    // Fallback for unknown roles
    return <Navigate to="/login" replace />;
  }

  // If not authenticated, redirect to login
  return <Navigate to="/login" replace />;
}